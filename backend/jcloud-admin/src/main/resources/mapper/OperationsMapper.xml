<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jcloud.admin.mapper.OperationsMapper">

    <!-- 主播统计数据结果映射 -->
    <resultMap id="AnchorStatsResultMap" type="com.jcloud.admin.dto.response.AnchorStatsResponse">
        <result column="total_recharge" property="totalRecharge" jdbcType="DECIMAL"/>
        <result column="total_consume" property="totalConsume" jdbcType="DECIMAL"/>
        <result column="user_count" property="userCount" jdbcType="INTEGER"/>
        <result column="period_new_user_count" property="periodNewUserCount" jdbcType="INTEGER"/>
        <result column="period_new_invite_count" property="periodNewInviteCount" jdbcType="INTEGER"/>
        <result column="total_claim_amount" property="totalClaimAmount" jdbcType="DECIMAL"/>
        <result column="total_shipped_amount" property="totalShippedAmount" jdbcType="DECIMAL"/>
        <result column="total_backpack_amount" property="totalBackpackAmount" jdbcType="DECIMAL"/>
        <result column="period_total_recharge" property="periodTotalRecharge" jdbcType="DECIMAL"/>
        <result column="total_turnover" property="totalTurnover" jdbcType="DECIMAL"/>
        <result column="profit_ratio" property="profitRatio" jdbcType="DECIMAL"/>
        <result column="actual_profit" property="actualProfit" jdbcType="DECIMAL"/>
        <result column="start_time" property="startTime" jdbcType="INTEGER"/>
        <result column="end_time" property="endTime" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 首充统计数据结果映射 -->
    <resultMap id="FirstRechargeStatsResultMap" type="com.jcloud.admin.dto.response.FirstRechargeStatsResponse">
        <result column="first_recharge_user_count" property="firstRechargeUserCount" jdbcType="INTEGER"/>
        <result column="total_sub_user_count" property="totalSubUserCount" jdbcType="INTEGER"/>
        <result column="first_recharge_conversion_rate" property="firstRechargeConversionRate" jdbcType="DECIMAL"/>
        <result column="total_first_recharge_amount" property="totalFirstRechargeAmount" jdbcType="DECIMAL"/>
        <result column="avg_first_recharge_amount" property="avgFirstRechargeAmount" jdbcType="DECIMAL"/>
        <result column="period_first_recharge_user_count" property="periodFirstRechargeUserCount" jdbcType="INTEGER"/>
        <result column="period_first_recharge_amount" property="periodFirstRechargeAmount" jdbcType="DECIMAL"/>
        <result column="period_avg_first_recharge_amount" property="periodAvgFirstRechargeAmount" jdbcType="DECIMAL"/>
        <result column="start_time" property="startTime" jdbcType="INTEGER"/>
        <result column="end_time" property="endTime" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 主播列表结果映射 -->
    <resultMap id="AnchorListResultMap" type="com.jcloud.admin.dto.response.AnchorListResponse">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="nickname" property="nickname" jdbcType="VARCHAR"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="userimage" property="userimage" jdbcType="VARCHAR"/>
        <result column="state" property="state" jdbcType="INTEGER"/>
        <result column="identity" property="identity" jdbcType="INTEGER"/>
        <result column="isauth" property="isauth" jdbcType="INTEGER"/>
        <result column="coin" property="coin" jdbcType="DECIMAL"/>
        <result column="key" property="key" jdbcType="DECIMAL"/>
        <result column="sub_user_count" property="subUserCount" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="INTEGER"/>
        <result column="last_login_time" property="lastLoginTime" jdbcType="INTEGER"/>
        <result column="last_login_ip" property="lastLoginIp" jdbcType="VARCHAR"/>
        <result column="invite_code" property="inviteCode" jdbcType="VARCHAR"/>
        <result column="level" property="level" jdbcType="INTEGER"/>
        <result column="exp" property="exp" jdbcType="DECIMAL"/>
        <result column="managerId" property="managerId" jdbcType="VARCHAR"/>
        <result column="managerName" property="managerName" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 下级用户结果映射 -->
    <resultMap id="SubUserResultMap" type="com.jcloud.admin.dto.response.SubUserResponse">
        <result column="id" property="id" jdbcType="INTEGER"/>
        <result column="nickname" property="nickname" jdbcType="VARCHAR"/>
        <result column="username" property="username" jdbcType="VARCHAR"/>
        <result column="phone" property="phone" jdbcType="VARCHAR"/>
        <result column="userimage" property="userimage" jdbcType="VARCHAR"/>
        <result column="state" property="state" jdbcType="INTEGER"/>
        <result column="isauth" property="isauth" jdbcType="INTEGER"/>
        <result column="coin" property="coin" jdbcType="DECIMAL"/>
        <result column="key" property="key" jdbcType="DECIMAL"/>
        <result column="create_time" property="createTime" jdbcType="INTEGER"/>
        <result column="last_login_time" property="lastLoginTime" jdbcType="INTEGER"/>
        <result column="total_recharge" property="totalRecharge" jdbcType="DECIMAL"/>
        <result column="total_consume" property="totalConsume" jdbcType="DECIMAL"/>
        <result column="first_recharge_time" property="firstRechargeTime" jdbcType="INTEGER"/>
        <result column="first_recharge_amount" property="firstRechargeAmount" jdbcType="DECIMAL"/>
        <result column="has_first_recharge" property="hasFirstRecharge" jdbcType="BOOLEAN"/>
        <result column="level" property="level" jdbcType="INTEGER"/>
        <result column="exp" property="exp" jdbcType="DECIMAL"/>
        <result column="invite_user" property="inviteUser" jdbcType="INTEGER"/>
    </resultMap>

    <!-- 消费详情结果映射 -->
    <resultMap id="ConsumeDetailResultMap" type="com.jcloud.admin.dto.response.ConsumeDetailResponse">
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="uid" property="uid" jdbcType="INTEGER"/>
        <result column="nickname" property="nickname" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="DECIMAL"/>
        <result column="balance" property="balance" jdbcType="DECIMAL"/>
        <result column="time" property="time" jdbcType="INTEGER"/>
        <result column="info" property="info" jdbcType="VARCHAR"/>
        <result column="consume_type" property="consumeType" jdbcType="VARCHAR"/>
        <result column="is_after_first_recharge" property="isAfterFirstRecharge" jdbcType="BOOLEAN"/>
    </resultMap>

    <!-- 充值详情结果映射 -->
    <resultMap id="RechargeDetailResultMap" type="com.jcloud.admin.dto.response.RechargeDetailResponse">
        <result column="id" property="id" jdbcType="VARCHAR"/>
        <result column="payid" property="payid" jdbcType="VARCHAR"/>
        <result column="uid" property="uid" jdbcType="INTEGER"/>
        <result column="nickname" property="nickname" jdbcType="VARCHAR"/>
        <result column="amount" property="amount" jdbcType="DECIMAL"/>
        <result column="coin" property="coin" jdbcType="DECIMAL"/>
        <result column="state" property="state" jdbcType="INTEGER"/>
        <result column="state_desc" property="stateDesc" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="INTEGER"/>
        <result column="update_time" property="updateTime" jdbcType="INTEGER"/>
        <result column="is_first_recharge" property="isFirstRecharge" jdbcType="BOOLEAN"/>
        <result column="payment_method" property="paymentMethod" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 
        调用主播统计存储过程
        使用从库数据源查询主播统计数据
    -->
    <select id="getAnchorStatistics" resultMap="AnchorStatsResultMap" timeout="30">
        {CALL GetInviteUserStatistics(
            #{inviteUserId,jdbcType=INTEGER,mode=IN}, 
            #{startTime,jdbcType=INTEGER,mode=IN}, 
            #{endTime,jdbcType=INTEGER,mode=IN}
        )}
    </select>

    <!-- 获取主播首充统计数据 -->
    <select id="getFirstRechargeStats" resultMap="FirstRechargeStatsResultMap">
        SELECT COUNT(DISTINCT CASE WHEN fr.id IS NOT NULL THEN u.id END) as first_recharge_user_count,
               COUNT(DISTINCT u.id)                                      as total_sub_user_count,
               ROUND(
                       COUNT(DISTINCT CASE WHEN fr.id IS NOT NULL THEN u.id END) * 100.0 /
                       NULLIF(COUNT(DISTINCT u.id), 0), 2
               )                                                         as first_recharge_conversion_rate,
               COALESCE(SUM(fr.amount), 0)                               as total_first_recharge_amount,
               ROUND(
                       COALESCE(SUM(fr.amount), 0) /
                       NULLIF(COUNT(DISTINCT CASE WHEN fr.id IS NOT NULL THEN u.id END), 0), 2
               )                                                         as avg_first_recharge_amount,
               COUNT(DISTINCT CASE
                                  WHEN fr.id IS NOT NULL
                                      AND (#{startTime} IS NULL OR fr.create_time &gt;= #{startTime})
                                      AND (#{endTime} IS NULL OR fr.create_time &lt;= #{endTime})
                                      THEN u.id
                   END)                                                  as period_first_recharge_user_count,
               COALESCE(SUM(CASE
                                WHEN (#{startTime} IS NULL OR fr.create_time &gt;= #{startTime})
                                    AND (#{endTime} IS NULL OR fr.create_time &lt;= #{endTime})
                                    THEN fr.amount
                                ELSE 0
                   END), 0)                                              as period_first_recharge_amount,
               ROUND(
                       COALESCE(SUM(CASE
                                        WHEN (#{startTime} IS NULL OR fr.create_time &gt;= #{startTime})
                                            AND (#{endTime} IS NULL OR fr.create_time &lt;= #{endTime})
                                            THEN fr.amount
                                        ELSE 0
                           END), 0) /
                       NULLIF(COUNT(DISTINCT CASE
                                                 WHEN fr.id IS NOT NULL
                                                     AND (#{startTime} IS NULL OR fr.create_time &gt;= #{startTime})
                                                     AND (#{endTime} IS NULL OR fr.create_time &lt;= #{endTime})
                                                     THEN u.id
                           END), 0), 2
               )                                                         as period_avg_first_recharge_amount,
               #{startTime}                                              as start_time,
               #{endTime}                                                as end_time
        FROM vim_user u
                 LEFT JOIN (SELECT r.uid,
                                   r.id,
                                   r.amount,
                                   r.create_time,
                                   ROW_NUMBER() OVER (PARTITION BY r.uid ORDER BY r.create_time) as rn
                            FROM vim_order_recharge r
                            WHERE r.state = 2) fr ON u.id = fr.uid AND fr.rn = 1
        WHERE u.invite_user = #{anchorId}
    </select>

    <!-- 查询主播列表（手动分页） -->
    <select id="selectAnchorList" resultMap="AnchorListResultMap">
        SELECT
            u.id,
            u.nickname,
            u.username,
            u.phone,
            u.userimage,
            u.state,
            u.identity,
            u.isauth,
            u.coin,
            u.key,
            u.create_time,
            u.last_login_time,
            u.last_login_ip,
            u.invite_code,
            u.level,
            u.exp,
            COALESCE(sub_count.cnt, 0) as sub_user_count,
        inviter.id as managerId,
        inviter.nickname as managerName
        FROM vim_user u
        LEFT JOIN (
            SELECT invite_user, COUNT(*) as cnt
            FROM vim_user
            GROUP BY invite_user
        ) sub_count ON u.id = sub_count.invite_user
        LEFT JOIN vim_user inviter ON u.invite_user = inviter.id
        WHERE u.identity IN (2, 3)
        <if test="query.nickname != null and query.nickname != ''">
            AND u.nickname LIKE CONCAT('%', #{query.nickname}, '%')
        </if>
        <if test="query.status != null">
            AND u.state = #{query.status}
        </if>
        <if test="query.identity != null">
            AND u.identity = #{query.identity}
        </if>
        <if test="query.registerStartTime != null">
            AND u.create_time &gt;= #{query.registerStartTime}
        </if>
        <if test="query.registerEndTime != null">
            AND u.create_time &lt;= #{query.registerEndTime}
        </if>
        <if test="query.isAuth != null">
            AND u.isauth = #{query.isAuth}
        </if>
        <if test="query.phone != null and query.phone != ''">
            AND u.phone LIKE CONCAT('%', #{query.phone}, '%')
        </if>
        <!-- 数据权限过滤：根据可访问的手机号列表过滤 -->
        <if test="query.accessiblePhones != null and query.accessiblePhones.size() > 0">
            AND u.phone IN
            <foreach collection="query.accessiblePhones" item="phone" open="(" separator="," close=")">
                #{phone}
            </foreach>
        </if>
        ORDER BY
        <choose>
            <when test="query.orderBy != null and query.orderBy != ''">
                <choose>
                    <when test="query.orderBy == 'createTime'">u.create_time</when>
                    <when test="query.orderBy == 'lastLoginTime'">u.last_login_time</when>
                    <when test="query.orderBy == 'nickname'">u.nickname</when>
                    <when test="query.orderBy == 'username'">u.username</when>
                    <when test="query.orderBy == 'state'">u.state</when>
                    <when test="query.orderBy == 'identity'">u.identity</when>
                    <when test="query.orderBy == 'isauth'">u.isauth</when>
                    <when test="query.orderBy == 'coin'">u.coin</when>
                    <when test="query.orderBy == 'key'">u.key</when>
                    <when test="query.orderBy == 'level'">u.level</when>
                    <when test="query.orderBy == 'exp'">u.exp</when>
                    <when test="query.orderBy == 'subUserCount'">sub_user_count</when>
                    <otherwise>u.create_time</otherwise>
                </choose>
                <choose>
                    <when test="query.orderDirection != null and query.orderDirection.toUpperCase() == 'ASC'"> ASC</when>
                    <otherwise> DESC</otherwise>
                </choose>
            </when>
            <otherwise>
                u.create_time DESC
            </otherwise>
        </choose>
        LIMIT #{query.pageSize} OFFSET #{query.offset}
    </select>

    <!-- 统计主播列表总数 -->
    <select id="countAnchorList" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM vim_user u
        WHERE u.identity IN (2, 3)
        <if test="query.nickname != null and query.nickname != ''">
            AND u.nickname LIKE CONCAT('%', #{query.nickname}, '%')
        </if>
        <if test="query.status != null">
            AND u.state = #{query.status}
        </if>
        <if test="query.identity != null">
            AND u.identity = #{query.identity}
        </if>
        <if test="query.registerStartTime != null">
            AND u.create_time &gt;= #{query.registerStartTime}
        </if>
        <if test="query.registerEndTime != null">
            AND u.create_time &lt;= #{query.registerEndTime}
        </if>
        <if test="query.isAuth != null">
            AND u.isauth = #{query.isAuth}
        </if>
        <if test="query.phone != null and query.phone != ''">
            AND u.phone LIKE CONCAT('%', #{query.phone}, '%')
        </if>
        <!-- 数据权限过滤：根据可访问的手机号列表过滤 -->
        <if test="query.accessiblePhones != null and query.accessiblePhones.size() > 0">
            AND u.phone IN
            <foreach collection="query.accessiblePhones" item="phone" open="(" separator="," close=")">
                #{phone}
            </foreach>
        </if>
    </select>

    <!-- 分页查询主播下级用户 -->
    <select id="selectSubUsers" resultMap="SubUserResultMap">
        SELECT
            u.id,
            u.nickname,
            u.username,
            u.phone,
            u.userimage,
            u.state,
            u.isauth,
            u.coin,
            u.key,
            u.create_time,
            u.last_login_time,
            u.level,
            u.exp,
            u.invite_user,
            COALESCE(recharge_stats.total_recharge, 0) as total_recharge,
            COALESCE(consume_stats.total_consume, 0) as total_consume,
            first_recharge.create_time as first_recharge_time,
            first_recharge.amount as first_recharge_amount,
            CASE WHEN first_recharge.id IS NOT NULL THEN 1 ELSE 0 END as has_first_recharge
        FROM vim_user u
        LEFT JOIN (
            SELECT uid, SUM(amount) as total_recharge
            FROM vim_order_recharge
            WHERE state = 2
            GROUP BY uid
        ) recharge_stats ON u.id = recharge_stats.uid
        LEFT JOIN (
            SELECT uid, SUM(amount) as total_consume
            FROM vim_order_pay
            GROUP BY uid
        ) consume_stats ON u.id = consume_stats.uid
        LEFT JOIN (
            SELECT r.uid, r.id, r.amount, r.create_time,
                   ROW_NUMBER() OVER (PARTITION BY r.uid ORDER BY r.create_time ASC) as rn
            FROM vim_order_recharge r
            WHERE r.state = 2
        ) first_recharge ON u.id = first_recharge.uid AND first_recharge.rn = 1
        WHERE u.invite_user = #{anchorId}
        <if test="query.nickname != null and query.nickname != ''">
            AND u.nickname LIKE CONCAT('%', #{query.nickname}, '%')
        </if>
        <if test="query.username != null and query.username != ''">
            AND u.username LIKE CONCAT('%', #{query.username}, '%')
        </if>
        <if test="query.phone != null and query.phone != ''">
            AND u.phone LIKE CONCAT('%', #{query.phone}, '%')
        </if>
        <if test="query.state != null">
            AND u.state = #{query.state}
        </if>
        <if test="query.isAuth != null">
            AND u.isauth = #{query.isAuth}
        </if>
        <if test="query.registerStartTime != null">
            AND u.create_time &gt;= #{query.registerStartTime}
        </if>
        <if test="query.registerEndTime != null">
            AND u.create_time &lt;= #{query.registerEndTime}
        </if>
        <if test="query.hasFirstRecharge != null">
            <choose>
                <when test="query.hasFirstRecharge == true">
                    AND first_recharge.id IS NOT NULL
                </when>
                <otherwise>
                    AND first_recharge.id IS NULL
                </otherwise>
            </choose>
        </if>
        <if test="query.minRechargeAmount != null">
            AND COALESCE(recharge_stats.total_recharge, 0) &gt;= #{query.minRechargeAmount}
        </if>
        <if test="query.maxRechargeAmount != null">
            AND COALESCE(recharge_stats.total_recharge, 0) &lt;= #{query.maxRechargeAmount}
        </if>
        ORDER BY
        <choose>
            <when test="query.orderBy != null and query.orderBy != ''">
                <choose>
                    <when test="query.orderBy == 'createTime'">u.create_time</when>
                    <when test="query.orderBy == 'lastLoginTime'">u.last_login_time</when>
                    <when test="query.orderBy == 'nickname'">u.nickname</when>
                    <when test="query.orderBy == 'username'">u.username</when>
                    <when test="query.orderBy == 'state'">u.state</when>
                    <when test="query.orderBy == 'coin'">u.coin</when>
                    <when test="query.orderBy == 'key'">u.key</when>
                    <when test="query.orderBy == 'level'">u.level</when>
                    <when test="query.orderBy == 'exp'">u.exp</when>
                    <when test="query.orderBy == 'totalRecharge'">total_recharge</when>
                    <when test="query.orderBy == 'totalConsume'">total_consume</when>
                    <when test="query.orderBy == 'firstRechargeTime'">first_recharge_time</when>
                    <when test="query.orderBy == 'firstRechargeAmount'">first_recharge_amount</when>
                    <otherwise>u.create_time</otherwise>
                </choose>
                <choose>
                    <when test="query.orderDirection != null and query.orderDirection.toUpperCase() == 'ASC'"> ASC</when>
                    <otherwise> DESC</otherwise>
                </choose>
            </when>
            <otherwise>
                u.create_time DESC
            </otherwise>
        </choose>
        LIMIT #{query.pageSize} OFFSET #{query.offset}
    </select>

    <!-- 统计主播下级用户总数 -->
    <select id="countSubUsers" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM vim_user u
        WHERE u.invite_user = #{anchorId}
        <if test="query.nickname != null and query.nickname != ''">
            AND u.nickname LIKE CONCAT('%', #{query.nickname}, '%')
        </if>
        <if test="query.username != null and query.username != ''">
            AND u.username LIKE CONCAT('%', #{query.username}, '%')
        </if>
        <if test="query.phone != null and query.phone != ''">
            AND u.phone LIKE CONCAT('%', #{query.phone}, '%')
        </if>
        <if test="query.state != null">
            AND u.state = #{query.state}
        </if>
        <if test="query.isAuth != null">
            AND u.isauth = #{query.isAuth}
        </if>
        <if test="query.registerStartTime != null">
            AND u.create_time &gt;= #{query.registerStartTime}
        </if>
        <if test="query.registerEndTime != null">
            AND u.create_time &lt;= #{query.registerEndTime}
        </if>
        <if test="query.hasFirstRecharge != null">
            <choose>
                <when test="query.hasFirstRecharge == true">
                    AND EXISTS (SELECT 1 FROM vim_order_recharge r WHERE r.uid = u.id AND r.state = 2)
                </when>
                <otherwise>
                    AND NOT EXISTS (SELECT 1 FROM vim_order_recharge r WHERE r.uid = u.id AND r.state = 2)
                </otherwise>
            </choose>
        </if>
        <if test="query.minRechargeAmount != null">
            AND COALESCE((SELECT SUM(amount) FROM vim_order_recharge r WHERE r.uid = u.id AND r.state = 2), 0) &gt;= #{query.minRechargeAmount}
        </if>
        <if test="query.maxRechargeAmount != null">
            AND COALESCE((SELECT SUM(amount) FROM vim_order_recharge r WHERE r.uid = u.id AND r.state = 2), 0) &lt;= #{query.maxRechargeAmount}
        </if>
    </select>

    <!-- 分页查询用户消费详情 -->
    <select id="selectConsumeDetails" resultMap="ConsumeDetailResultMap">
        SELECT
            p.id,
            p.uid,
            u.nickname,
            p.amount,
            p.balance,
            p.time,
            p.info,
            CASE
                WHEN p.info LIKE '%购买%' OR p.info LIKE '%买%' THEN '购买道具'
                WHEN p.info LIKE '%开箱%' OR p.info LIKE '%开启%' THEN '开箱消费'
                WHEN p.info LIKE '%锻造%' OR p.info LIKE '%合成%' THEN '锻造消费'
                ELSE '其他消费'
            END as consume_type,
            CASE
                WHEN first_recharge.create_time IS NOT NULL AND p.time > first_recharge.create_time THEN 1
                ELSE 0
            END as is_after_first_recharge
        FROM vim_order_pay p
        LEFT JOIN vim_user u ON p.uid = u.id
        LEFT JOIN (
            SELECT r.uid, r.create_time,
                   ROW_NUMBER() OVER (PARTITION BY r.uid ORDER BY r.create_time ASC) as rn
            FROM vim_order_recharge r
            WHERE r.state = 2
        ) first_recharge ON p.uid = first_recharge.uid AND first_recharge.rn = 1
        WHERE p.uid = #{userId}
        <if test="query.orderId != null and query.orderId != ''">
            AND p.id LIKE CONCAT('%', #{query.orderId}, '%')
        </if>
        <if test="query.startTime != null">
            AND p.time &gt;= #{query.startTime}
        </if>
        <if test="query.endTime != null">
            AND p.time &lt;= #{query.endTime}
        </if>
        <if test="query.minAmount != null">
            AND p.amount &gt;= #{query.minAmount}
        </if>
        <if test="query.maxAmount != null">
            AND p.amount &lt;= #{query.maxAmount}
        </if>
        <if test="query.info != null and query.info != ''">
            AND p.info LIKE CONCAT('%', #{query.info}, '%')
        </if>
        ORDER BY
        <choose>
            <when test="query.orderBy != null and query.orderBy != ''">
                <choose>
                    <when test="query.orderBy == 'time'">p.time</when>
                    <when test="query.orderBy == 'amount'">p.amount</when>
                    <when test="query.orderBy == 'balance'">p.balance</when>
                    <when test="query.orderBy == 'nickname'">u.nickname</when>
                    <otherwise>p.time</otherwise>
                </choose>
                <choose>
                    <when test="query.orderDirection != null and query.orderDirection.toUpperCase() == 'ASC'"> ASC</when>
                    <otherwise> DESC</otherwise>
                </choose>
            </when>
            <otherwise>
                p.time DESC
            </otherwise>
        </choose>
        LIMIT #{query.pageSize} OFFSET #{query.offset}
    </select>

    <!-- 统计用户消费详情总数 -->
    <select id="countConsumeDetails" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM vim_order_pay p
        WHERE p.uid = #{userId}
        <if test="query.orderId != null and query.orderId != ''">
            AND p.id LIKE CONCAT('%', #{query.orderId}, '%')
        </if>
        <if test="query.startTime != null">
            AND p.time &gt;= #{query.startTime}
        </if>
        <if test="query.endTime != null">
            AND p.time &lt;= #{query.endTime}
        </if>
        <if test="query.minAmount != null">
            AND p.amount &gt;= #{query.minAmount}
        </if>
        <if test="query.maxAmount != null">
            AND p.amount &lt;= #{query.maxAmount}
        </if>
        <if test="query.info != null and query.info != ''">
            AND p.info LIKE CONCAT('%', #{query.info}, '%')
        </if>
    </select>

    <!-- 分页查询用户充值详情 -->
    <select id="selectRechargeDetails" resultMap="RechargeDetailResultMap">
        SELECT
            r.id,
            r.payid,
            r.uid,
            u.nickname,
            r.amount,
            r.coin,
            r.state,
            CASE r.state
                WHEN 1 THEN '未支付'
                WHEN 2 THEN '已支付'
                WHEN 3 THEN '已支付但回调异常'
                ELSE '未知'
            END as state_desc,
            r.create_time,
            r.update_time,
            CASE
                WHEN first_recharge.rn = 1 THEN 1
                ELSE 0
            END as is_first_recharge,
            CASE
                WHEN r.payid LIKE 'wx%' OR r.payid LIKE '%wechat%' THEN '微信支付'
                WHEN r.payid LIKE 'ali%' OR r.payid LIKE '%alipay%' THEN '支付宝'
                ELSE '其他'
            END as payment_method
        FROM vim_order_recharge r
        LEFT JOIN vim_user u ON r.uid = u.id
        LEFT JOIN (
            SELECT uid, id, ROW_NUMBER() OVER (PARTITION BY uid ORDER BY create_time ASC) as rn
            FROM vim_order_recharge
            WHERE state = 2
        ) first_recharge ON r.uid = first_recharge.uid AND r.id = first_recharge.id
        WHERE r.uid = #{userId}
        <if test="query.orderId != null and query.orderId != ''">
            AND r.id LIKE CONCAT('%', #{query.orderId}, '%')
        </if>
        <if test="query.payId != null and query.payId != ''">
            AND r.payid LIKE CONCAT('%', #{query.payId}, '%')
        </if>
        <if test="query.startTime != null">
            AND r.create_time &gt;= #{query.startTime}
        </if>
        <if test="query.endTime != null">
            AND r.create_time &lt;= #{query.endTime}
        </if>
        <if test="query.minAmount != null">
            AND r.amount &gt;= #{query.minAmount}
        </if>
        <if test="query.maxAmount != null">
            AND r.amount &lt;= #{query.maxAmount}
        </if>
        <if test="query.state != null">
            AND r.state = #{query.state}
        </if>
        <if test="query.isFirstRecharge != null">
            <choose>
                <when test="query.isFirstRecharge == true">
                    AND first_recharge.rn = 1
                </when>
                <otherwise>
                    AND (first_recharge.rn IS NULL OR first_recharge.rn > 1)
                </otherwise>
            </choose>
        </if>
        ORDER BY
        <choose>
            <when test="query.orderBy != null and query.orderBy != ''">
                <choose>
                    <when test="query.orderBy == 'createTime'">r.create_time</when>
                    <when test="query.orderBy == 'updateTime'">r.update_time</when>
                    <when test="query.orderBy == 'amount'">r.amount</when>
                    <when test="query.orderBy == 'coin'">r.coin</when>
                    <when test="query.orderBy == 'state'">r.state</when>
                    <when test="query.orderBy == 'nickname'">u.nickname</when>
                    <otherwise>r.create_time</otherwise>
                </choose>
                <choose>
                    <when test="query.orderDirection != null and query.orderDirection.toUpperCase() == 'ASC'"> ASC</when>
                    <otherwise> DESC</otherwise>
                </choose>
            </when>
            <otherwise>
                r.create_time DESC
            </otherwise>
        </choose>
        LIMIT #{query.pageSize} OFFSET #{query.offset}
    </select>

    <!-- 统计用户充值详情总数 -->
    <select id="countRechargeDetails" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM vim_order_recharge r
        WHERE r.uid = #{userId}
        <if test="query.orderId != null and query.orderId != ''">
            AND r.id LIKE CONCAT('%', #{query.orderId}, '%')
        </if>
        <if test="query.payId != null and query.payId != ''">
            AND r.payid LIKE CONCAT('%', #{query.payId}, '%')
        </if>
        <if test="query.startTime != null">
            AND r.create_time &gt;= #{query.startTime}
        </if>
        <if test="query.endTime != null">
            AND r.create_time &lt;= #{query.endTime}
        </if>
        <if test="query.minAmount != null">
            AND r.amount &gt;= #{query.minAmount}
        </if>
        <if test="query.maxAmount != null">
            AND r.amount &lt;= #{query.maxAmount}
        </if>
        <if test="query.state != null">
            AND r.state = #{query.state}
        </if>
        <if test="query.isFirstRecharge != null">
            <choose>
                <when test="query.isFirstRecharge == true">
                    AND EXISTS (
                        SELECT 1 FROM (
                            SELECT uid, id, ROW_NUMBER() OVER (PARTITION BY uid ORDER BY create_time ASC) as rn
                            FROM vim_order_recharge
                            WHERE state = 2
                        ) first_recharge
                        WHERE first_recharge.uid = r.uid AND first_recharge.id = r.id AND first_recharge.rn = 1
                    )
                </when>
                <otherwise>
                    AND NOT EXISTS (
                        SELECT 1 FROM (
                            SELECT uid, id, ROW_NUMBER() OVER (PARTITION BY uid ORDER BY create_time ASC) as rn
                            FROM vim_order_recharge
                            WHERE state = 2
                        ) first_recharge
                        WHERE first_recharge.uid = r.uid AND first_recharge.id = r.id AND first_recharge.rn = 1
                    )
                </otherwise>
            </choose>
        </if>
    </select>

    <!-- ==================== 运营统计查询SQL ==================== -->
    <!-- 获取总用户数（所有主播的下级用户总数，包括多层级） -->
    <select id="getTotalUsersCount" resultType="java.lang.Long">
        WITH RECURSIVE user_hierarchy AS (
            /* 第一层：直接下级用户 */
            SELECT u.id, u.invite_user, 1 as level
            FROM vim_user u
            WHERE u.invite_user IS NOT NULL
              AND u.invite_user BETWEEN 1 AND 10
              AND u.invite_user IN (
                  SELECT id FROM vim_user WHERE identity IN (2, 3)
              )

            UNION ALL

            /* 递归：下级的下级用户 */
            SELECT u.id, u.invite_user, uh.level + 1
            FROM vim_user u
            INNER JOIN user_hierarchy uh ON u.invite_user = uh.id
            WHERE uh.level &lt; #{maxLevel}  /* 基于用户角色权限的动态层级限制 */
        )
        SELECT COUNT(DISTINCT id) as total_users
        FROM user_hierarchy
    </select>



    <!-- 获取新增用户数（包括多层级下级用户） -->
    <select id="getNewUsersCount" resultType="java.lang.Long">
        WITH RECURSIVE user_hierarchy AS (
            /* 第一层：直接下级用户 */
            SELECT u.id, u.invite_user, u.create_time, 1 as level
            FROM vim_user u
            WHERE u.invite_user IS NOT NULL
              AND u.invite_user > 0
              AND u.invite_user IN (
                  SELECT id FROM vim_user WHERE identity IN (2, 3)
              )

            UNION ALL

            /* 递归：下级的下级用户 */
            SELECT u.id, u.invite_user, u.create_time, uh.level + 1
            FROM vim_user u
            INNER JOIN user_hierarchy uh ON u.invite_user = uh.id
            WHERE uh.level &lt; #{maxLevel}  /* 基于用户角色权限的动态层级限制 */
        )
        SELECT COUNT(DISTINCT id) as new_users
        FROM user_hierarchy
        WHERE create_time BETWEEN UNIX_TIMESTAMP(#{startTime}) AND UNIX_TIMESTAMP(#{endTime})
    </select>

    <!-- 获取总充值金额（包括多层级下级用户的充值） -->
    <select id="getTotalRechargeAmount" resultType="java.math.BigDecimal">
        WITH RECURSIVE user_hierarchy AS (
            /* 第一层：直接下级用户 */
            SELECT u.id, u.invite_user, 1 as level
            FROM vim_user u
            WHERE u.invite_user IS NOT NULL
              AND u.invite_user > 0
              AND u.invite_user IN (
                  SELECT id FROM vim_user WHERE identity IN (2, 3)
              )

            UNION ALL

            /* 递归：下级的下级用户 */
            SELECT u.id, u.invite_user, uh.level + 1
            FROM vim_user u
            INNER JOIN user_hierarchy uh ON u.invite_user = uh.id
            WHERE uh.level &lt; #{maxLevel}  /* 基于用户角色权限的动态层级限制 */
        )
        SELECT COALESCE(SUM(r.amount), 0) as total_recharge
        FROM vim_order_recharge r
        INNER JOIN user_hierarchy uh ON r.uid = uh.id
        WHERE r.create_time BETWEEN UNIX_TIMESTAMP(#{startTime}) AND UNIX_TIMESTAMP(#{endTime})
          AND r.state = 2  /* 只统计成功的充值 */
    </select>

    <!-- 获取总消费金额（包括多层级下级用户的消费） -->
    <select id="getTotalConsumeAmount" resultType="java.math.BigDecimal">
        WITH RECURSIVE user_hierarchy AS (
            /* 第一层：直接下级用户 */
            SELECT u.id, u.invite_user, 1 as level
            FROM vim_user u
            WHERE u.invite_user IS NOT NULL
              AND u.invite_user > 0
              AND u.invite_user IN (
                  SELECT id FROM vim_user WHERE identity IN (2, 3)
              )
            UNION ALL

            /* 递归：下级的下级用户 */
            SELECT u.id, u.invite_user, uh.level + 1
            FROM vim_user u
            INNER JOIN user_hierarchy uh ON u.invite_user = uh.id
            WHERE uh.level &lt; #{maxLevel}  /* 基于用户角色权限的动态层级限制 */
        )
        SELECT COALESCE(SUM(ABS(c.coin)), 0) as total_consume
        FROM vim_user_coin_log c
        INNER JOIN user_hierarchy uh ON c.uid = uh.id
        WHERE c.time BETWEEN UNIX_TIMESTAMP(#{startTime}) AND UNIX_TIMESTAMP(#{endTime})
          AND c.type = 2  /* 只统计消费类型的记录 */
          AND c.coin &lt; 0  /* 消费是负数 */
    </select>

    <!-- 调用GetSubordinateProfit存储过程 -->
    <select id="getSubordinateProfit" resultType="com.jcloud.admin.dto.response.SubordinateProfit" timeout="30">
        {CALL GetSubordinateProfit(
            #{rootUid,jdbcType=INTEGER,mode=IN},
            #{maxLevel,jdbcType=INTEGER,mode=IN},
            #{startTime,jdbcType=VARCHAR,mode=IN},
            #{endTime,jdbcType=VARCHAR,mode=IN}
        )}
    </select>
</mapper>
