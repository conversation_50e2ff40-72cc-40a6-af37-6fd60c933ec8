/**
 * 权限常量定义
 * 
 * 统一管理系统中所有的权限标识，确保前后端权限标识一致性
 * 
 * <AUTHOR>
 * @since 1.0.0
 */

/**
 * 系统权限常量
 */
export const PERMISSIONS = {
  /**
   * 仪表板权限
   */
  DASHBOARD: {
    VIEW: 'dashboard:view',
  },

  /**
   * 系统管理权限
   */
  SYSTEM: {
    VIEW: 'system:view',
    
    /**
     * 用户管理权限
     */
    USER: {
      LIST: 'system:user:list',
      VIEW: 'system:user:query',
      ADD: 'system:user:add',
      EDIT: 'system:user:edit',
      DELETE: 'system:user:delete',
      SYNC: 'system:user:sync',
      RESET_PASSWORD: 'system:user:resetPwd',
    },

    /**
     * 角色管理权限
     */
    ROLE: {
      LIST: 'system:role:list',
      VIEW: 'system:role:query',
      ADD: 'system:role:add',
      EDIT: 'system:role:edit',
      DELETE: 'system:role:delete',
      ASSIGN_PERMISSIONS: 'system:role:assign-permissions',
      AUTH: 'system:role:auth',
    },

    /**
     * 权限管理权限
     */
    PERMISSION: {
      ADD: 'system:permission:add',
      EDIT: 'system:permission:edit',
      DELETE: 'system:permission:delete',
    },

    /**
     * 部门管理权限
     */
    DEPT: {
      LIST: 'system:dept:list',
      VIEW: 'system:dept:view',
      ADD: 'system:dept:add',
      EDIT: 'system:dept:edit',
      DELETE: 'system:dept:delete',
      QUERY: 'system:dept:query',
    },

    /**
     * 租户管理权限
     */
    TENANT: {
      LIST: 'system:tenant:list',
      VIEW: 'system:tenant:view',
      ADD: 'system:tenant:add',
      EDIT: 'system:tenant:edit',
      DELETE: 'system:tenant:delete',
      QUERY: 'system:tenant:query',
    },
  },

  /**
   * 权限管理模块权限（与后端菜单结构保持一致）
   */
  AUTH: {
    VIEW: 'auth:view',
  },

  /**
   * 菜单管理权限
   */
  MENU: {
    LIST: 'system:menu:list',
    VIEW: 'system:menu:view',
    QUERY: 'system:menu:query',
    ADD: 'system:menu:add',
    EDIT: 'system:menu:edit',
    DELETE: 'system:menu:delete',
    IMPORT: 'system:menu:import',
    EXPORT: 'system:menu:export',
  },

  /**
   * 缓存管理权限
   */
  CACHE: {
    MANAGE: 'system:cache:manage',
  },

  /**
   * 财务数据管理权限
   */
  FINANCIAL: {
    VIEW: 'financial:view',

    /**
     * 财务统计权限
     */
    STATS: {
      LIST: 'financial:stats:list',
      QUERY: 'financial:stats:query',
      VIEW: 'financial:stats:view',
      EXPORT: 'financial:stats:export',
    },
  },

  /**
   * 监控管理权限
   */
  MONITOR: {
    VIEW: 'monitor:view',

    /**
     * 操作日志权限
     */
    OPERLOG: {
      LIST: 'monitor:operlog:list',
    },

    /**
     * 系统信息权限
     */
    SERVER: {
      LIST: 'monitor:server:list',
    },
  },

  /**
   * 运营管理权限
   */
  OPERATIONS: {
    VIEW: 'operations:view',

    /**
     * 主播管理权限
     */
    ANCHOR: {
      LIST: 'operations:anchors:list',
      VIEW: 'operations:anchors:view',
      QUERY: 'operations:anchors:query',
      DETAIL: 'operations:anchors:detail',
      STATS: 'operations:anchor:stats',
      FIRST_RECHARGE: 'operations:anchors:first-recharge',
      SUB_USERS: 'operations:user:list',
      USERS: 'operations:anchors:users',
    },

    /**
     * 用户管理权限
     */
    USER: {
      CONSUME: 'operations:user:consume',
      RECHARGE: 'operations:user:recharge',
    },

    /**
     * 统计数据权限
     */
    STATS: {
      VIEW_PROFIT: 'operations:stats:view-profit',           // 查看实际利润
      VIEW_PENDING_AMOUNT: 'operations:stats:view-pending',  // 查看待发货金额
      VIEW_SHIPPED_AMOUNT: 'operations:stats:view-shipped',  // 查看实际发货金额
      VIEW_BACKPACK_VALUE: 'operations:stats:view-backpack', // 查看背包总价值
      VIEW_FINANCIAL: 'operations:stats:view-financial',     // 查看财务相关数据（包含以上所有）
    },
  },
} as const

/**
 * 角色常量定义
 */
export const ROLES = {
  /**
   * 超级管理员角色
   */
  SUPER_ADMIN: 'SUPER_ADMIN',
  
  /**
   * 系统管理员角色
   */
  ADMIN: 'ADMIN',
  
  /**
   * 普通用户角色
   */
  USER: 'USER',
  
  /**
   * 租户管理员角色
   */
  TENANT_ADMIN: 'TENANT_ADMIN',
} as const

/**
 * 特殊权限常量
 */
export const SPECIAL_PERMISSIONS = {
  /**
   * 超级管理员通配符权限
   */
  SUPER_ADMIN_WILDCARD: '*:*:*',
  
  /**
   * 所有权限通配符
   */
  ALL_PERMISSIONS: '*',
} as const

/**
 * 权限类型定义
 */
export type PermissionKey =
  | typeof PERMISSIONS.DASHBOARD[keyof typeof PERMISSIONS.DASHBOARD]
  | typeof PERMISSIONS.SYSTEM.USER[keyof typeof PERMISSIONS.SYSTEM.USER]
  | typeof PERMISSIONS.SYSTEM.ROLE[keyof typeof PERMISSIONS.SYSTEM.ROLE]
  | typeof PERMISSIONS.SYSTEM.PERMISSION[keyof typeof PERMISSIONS.SYSTEM.PERMISSION]
  | typeof PERMISSIONS.SYSTEM.DEPT[keyof typeof PERMISSIONS.SYSTEM.DEPT]
  | typeof PERMISSIONS.SYSTEM.TENANT[keyof typeof PERMISSIONS.SYSTEM.TENANT]
  | typeof PERMISSIONS.AUTH[keyof typeof PERMISSIONS.AUTH]
  | typeof PERMISSIONS.MENU[keyof typeof PERMISSIONS.MENU]
  | typeof PERMISSIONS.CACHE[keyof typeof PERMISSIONS.CACHE]
  | typeof PERMISSIONS.FINANCIAL.STATS[keyof typeof PERMISSIONS.FINANCIAL.STATS]
  | typeof PERMISSIONS.MONITOR.OPERLOG[keyof typeof PERMISSIONS.MONITOR.OPERLOG]
  | typeof PERMISSIONS.MONITOR.SERVER[keyof typeof PERMISSIONS.MONITOR.SERVER]
  | typeof PERMISSIONS.OPERATIONS.ANCHOR[keyof typeof PERMISSIONS.OPERATIONS.ANCHOR]
  | typeof PERMISSIONS.OPERATIONS.USER[keyof typeof PERMISSIONS.OPERATIONS.USER]
  | typeof PERMISSIONS.OPERATIONS.STATS[keyof typeof PERMISSIONS.OPERATIONS.STATS]
  | typeof SPECIAL_PERMISSIONS[keyof typeof SPECIAL_PERMISSIONS]

/**
 * 角色类型定义
 */
export type RoleKey = typeof ROLES[keyof typeof ROLES]

/**
 * 权限工具函数
 */
export const PermissionUtils = {
  /**
   * 检查权限是否为通配符权限
   */
  isWildcardPermission: (permission: string): boolean => {
    return permission.includes('*')
  },

  /**
   * 获取权限的模块前缀
   */
  getPermissionModule: (permission: string): string => {
    return permission.split(':')[0] || ''
  },

  /**
   * 获取权限的操作类型
   */
  getPermissionAction: (permission: string): string => {
    const parts = permission.split(':')
    return parts[parts.length - 1] || ''
  },

  /**
   * 构建权限标识
   */
  buildPermission: (module: string, resource: string, action: string): string => {
    return `${module}:${resource}:${action}`
  },

  /**
   * 验证权限格式
   */
  validatePermissionFormat: (permission: string): boolean => {
    // 权限格式: module:resource:action 或 通配符
    const wildcardPattern = /^(\*|\w+):(\*|\w+):(\*|\w+)$/
    const simplePattern = /^\*$/
    return wildcardPattern.test(permission) || simplePattern.test(permission)
  },

  /**
   * 获取所有权限列表（扁平化）
   */
  getAllPermissions: (): string[] => {
    const permissions: string[] = []
    
    const extractPermissions = (obj: any): void => {
      for (const key in obj) {
        if (typeof obj[key] === 'string') {
          permissions.push(obj[key])
        } else if (typeof obj[key] === 'object') {
          extractPermissions(obj[key])
        }
      }
    }
    
    extractPermissions(PERMISSIONS)
    extractPermissions(SPECIAL_PERMISSIONS)
    
    return permissions
  },

  /**
   * 根据模块获取权限列表
   */
  getPermissionsByModule: (module: keyof typeof PERMISSIONS): string[] => {
    const permissions: string[] = []
    const modulePerms = PERMISSIONS[module]
    
    const extractPermissions = (obj: any): void => {
      for (const key in obj) {
        if (typeof obj[key] === 'string') {
          permissions.push(obj[key])
        } else if (typeof obj[key] === 'object') {
          extractPermissions(obj[key])
        }
      }
    }
    
    extractPermissions(modulePerms)
    return permissions
  },
}
