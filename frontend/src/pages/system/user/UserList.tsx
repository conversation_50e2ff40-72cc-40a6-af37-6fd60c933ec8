/**
 * 用户管理页面
 *
 * 提供用户的增删改查、角色分配、状态管理等功能
 * 统一重构版本 - 完整的权限控制实现
 */

import React, { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle, Button, ConfirmDialog } from '@/components/ui'
import { Plus, Search, Filter, RefreshCw, Download, Upload, RotateCcw } from 'lucide-react'
import { usePageTitle } from '@/router'
import { PagePermissionWrapper, PermissionToolbar } from '@/components/auth/PermissionWrapper.tsx'
import UserSearchForm from './components/UserSearchForm'
import UserTable from './components/UserTable'
import UserForm from './components/UserForm'
import ResetPasswordDialog from './components/ResetPasswordDialog'
import UserRoleAssignDialog from './components/UserRoleAssignDialog'
import { UserService } from '@/services'
import type { User, UserQueryRequest } from '@/types'
import type { PageResult } from '@/types'

/**
 * 用户管理页面
 */
const UserList: React.FC = () => {
  // 设置页面标题
  usePageTitle('用户管理')

  // 数据状态
  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedIds, setSelectedIds] = useState<number[]>([])

  // 搜索参数状态
  const [searchParams, setSearchParams] = useState<UserQueryRequest>({
    pageNum: 1,
    pageSize: 10
  })

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  })

  // 表单相关状态
  const [formOpen, setFormOpen] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)

  // 重置密码对话框状态
  const [resetPasswordDialogOpen, setResetPasswordDialogOpen] = useState(false)
  const [resetPasswordUser, setResetPasswordUser] = useState<User | null>(null)

  // 角色分配对话框状态
  const [roleAssignDialogOpen, setRoleAssignDialogOpen] = useState(false)
  const [roleAssignUser, setRoleAssignUser] = useState<User | null>(null)

  // 确认对话框状态
  const [confirmDialog, setConfirmDialog] = useState<{
    open: boolean
    title: string
    description: string
    onConfirm: () => void
  }>({
    open: false,
    title: '',
    description: '',
    onConfirm: () => {}
  })

  // 排序状态
  const [sortConfig, setSortConfig] = useState<{
    field: string
    order: 'asc' | 'desc'
  }>()

  // 加载用户列表
  const loadUsers = useCallback(async () => {
    try {
      setLoading(true)
      const result: PageResult<User> = await UserService.pageUsers(searchParams)

      setUsers(result.records || [])

      // 更新分页信息
      setPagination({
        current: searchParams.pageNum || 1,
        pageSize: searchParams.pageSize || 10,
        total: result.total || 0
      })
    } catch (error) {
      console.error(' 加载用户列表失败:', error)
      setUsers([])
      setPagination({ current: 1, pageSize: 10, total: 0 })
    } finally {
      setLoading(false)
    }
  }, [searchParams])

  // 初始化加载数据
  useEffect(() => {
    loadUsers()
  }, [])

  // 监听搜索参数变化，重新加载数据
  useEffect(() => {
    console.log(' 搜索参数变化，重新加载数据:', searchParams)
    loadUsers()
  }, [searchParams, loadUsers])

  // 处理搜索参数变更
  const handleSearchChange = (params: UserQueryRequest) => {
    console.log('🔍 搜索参数变更:', params)
    setSearchParams(params)
  }

  // 处理搜索
  const handleSearch = () => {
    console.log('🔍 执行搜索')
    loadUsers()
  }

  // 处理重置
  const handleReset = () => {
    console.log('🔄 重置搜索条件')
    const resetParams: UserQueryRequest = {
      pageNum: 1,
      pageSize: searchParams.pageSize || 10
    }
    setSearchParams(resetParams)
  }

  // 处理分页变更
  const handlePaginationChange = (page: number, pageSize: number) => {
    console.log('🔄 分页变更:', { pageNum: page, pageSize })
    const newParams = {
      ...searchParams,
      pageNum: page,
      pageSize: pageSize
    }
    setSearchParams(newParams)
  }

  // 处理排序
  const handleSort = (field: string) => {
    console.log('🔄 排序变更:', field)
    let order: 'asc' | 'desc' = 'asc'

    if (sortConfig && sortConfig.field === field) {
      order = sortConfig.order === 'asc' ? 'desc' : 'asc'
    }

    setSortConfig({ field, order })

    // 更新搜索参数
    const newParams = {
      ...searchParams,
      orderBy: field,
      orderDirection: order.toUpperCase() as 'ASC' | 'DESC',
      pageNum: 1 // 排序时重置到第一页
    }
    setSearchParams(newParams)
  }

  // 处理新增用户
  const handleAdd = () => {
    console.log('➕ 新增用户')
    setEditingUser(null)
    setFormOpen(true)
  }

  // 处理编辑用户
  const handleEdit = (user: User) => {
    console.log('✏️ 编辑用户:', user)
    setEditingUser(user)
    setFormOpen(true)
  }

  // 处理用户删除
  const handleDelete = (user: User) => {
    console.log('🗑️ 删除用户:', user)

    // 显示确认对话框
    setConfirmDialog({
      open: true,
      title: '删除用户',
      description: `确定要删除用户"${user.username}"吗？此操作不可恢复，该用户的所有数据将被删除。`,
      onConfirm: async () => {
        try {
          await UserService.deleteUser(user.id)
          console.log('✅ 用户删除成功')

          // 重新加载数据
          await loadUsers()

          // 清空选择
          setSelectedIds([])
        } catch (error) {
          console.error('❌ 删除用户失败:', error)

          // 显示错误确认对话框
          setConfirmDialog({
            open: true,
            title: '删除用户失败',
            description: `删除用户"${user.username}"失败：\n\n${(error as Error).message}`,
            onConfirm: () => {
              // 错误对话框只需要确认按钮
            }
          })
        }
      }
    })
  }

  // 处理重置密码
  const handleResetPassword = (user: User) => {
    console.log('🔑 重置密码:', user)
    setResetPasswordUser(user)
    setResetPasswordDialogOpen(true)
  }

  // 处理重置密码成功
  const handleResetPasswordSuccess = async (newPassword: string) => {
    if (!resetPasswordUser) return

    try {
      await UserService.resetPassword({
        id: resetPasswordUser.id,
        newPassword: newPassword
      })
      console.log('✅ 密码重置成功')

      // 显示成功确认对话框
      setConfirmDialog({
        open: true,
        title: '密码重置成功',
        description: `用户"${resetPasswordUser.username}"的密码已重置成功。\n\n请提醒用户及时登录并修改密码。`,
        onConfirm: () => {
          // 成功对话框只需要确认按钮
        }
      })
    } catch (error) {
      console.error('❌ 重置密码失败:', error)

      // 显示错误确认对话框
      setConfirmDialog({
        open: true,
        title: '重置密码失败',
        description: `重置用户"${resetPasswordUser.username}"的密码失败：\n\n${(error as Error).message}`,
        onConfirm: () => {
          // 错误对话框只需要确认按钮
        }
      })
    }
  }

  // 处理角色分配
  const handleAssignRoles = (user: User) => {
    console.log('🛡️ 分配角色:', user)
    setRoleAssignUser(user)
    setRoleAssignDialogOpen(true)
  }

  // 处理角色分配成功
  const handleRoleAssignSuccess = () => {
    console.log('✅ 角色分配成功')
    // 重新加载用户列表以更新显示
    loadUsers()
  }

  // 处理状态切换
  const handleToggleStatus = (user: User) => {
    console.log('🔄 切换状态:', user)

    const newStatus = user.status === 1 ? 0 : 1
    const statusText = newStatus === 1 ? '启用' : '禁用'

    // 显示确认对话框
    setConfirmDialog({
      open: true,
      title: `${statusText}用户`,
      description: `确定要${statusText}用户"${user.username}"吗？`,
      onConfirm: async () => {
        try {
          if (newStatus === 1) {
            await UserService.enableUser(user.id)
          } else {
            await UserService.disableUser(user.id)
          }
          console.log(`✅ 用户${statusText}成功`)

          // 重新加载数据
          await loadUsers()
        } catch (error) {
          console.error(`❌ ${statusText}用户失败:`, error)

          // 显示错误确认对话框
          setConfirmDialog({
            open: true,
            title: `${statusText}用户失败`,
            description: `${statusText}用户"${user.username}"失败：\n\n${(error as Error).message}`,
            onConfirm: () => {
              // 错误对话框只需要确认按钮
            }
          })
        }
      }
    })
  }

  // 处理表单成功
  const handleFormSuccess = () => {
    console.log('✅ 表单操作成功')
    loadUsers()
    setSelectedIds([])
  }

  // 处理刷新
  const handleRefresh = () => {
    console.log('🔄 刷新数据')
    loadUsers()
  }

  // 处理导入
  const handleImport = () => {
    console.log('📥 导入用户')
    // TODO: 实现用户导入功能
  }

  // 处理导出
  const handleExport = () => {
    // TODO: 实现用户导出功能
  }

  // 处理同步主播和代理用户
  const handleSyncAnchorAndAgentUsers = () => {

    // 显示确认对话框
    setConfirmDialog({
      open: true,
      title: '同步主播和代理用户',
      description: '确定要从vim_user表同步主播和代理用户到当前系统吗？\n\n此操作将：\n• 查询从库中的主播用户（identity=2或3）和代理用户（identity=4）\n• 根据手机号去重，只同步不存在的用户\n• 为代理用户自动创建专属部门并分配下级主播\n• 采用异步处理，避免超时问题',
      onConfirm: async () => {
        try {
          setLoading(true)

          const result = await UserService.syncAnchorAndAgentUsers()

          // 显示任务启动成功对话框
          setConfirmDialog({
            open: true,
            title: '同步任务已启动',
            description: `${result}\n\n同步过程将在后台执行，请稍后刷新用户列表查看结果。`,
            onConfirm: () => {
              // 任务启动后可以选择刷新列表
              loadUsers()
            }
          })
        } catch (error) {
          console.error('❌ 同步失败:', error)

          // 显示错误对话框
          setConfirmDialog({
            open: true,
            title: '同步失败',
            description: `启动同步任务失败：\n\n${(error as Error).message}`,
            onConfirm: () => {
              // 错误对话框只需要确认按钮
            }
          })
        } finally {
          setLoading(false)
        }
      }
    })
  }

  return (
    <PagePermissionWrapper module="user">
      <div className="space-y-6">
        {/* 页面标题 */}
        <div>
          <h1 className="text-3xl font-bold tracking-tight">用户管理</h1>
          <p className="text-muted-foreground">
            管理系统用户，包括用户信息、角色分配、状态控制等
          </p>
        </div>

        {/* 权限控制的工具栏 */}
        <PermissionToolbar
          module="user"
          searchComponent={
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Search className="w-5 h-5 mr-2" />
                  搜索筛选
                </CardTitle>
              </CardHeader>
              <CardContent>
                <UserSearchForm
                  searchParams={searchParams}
                  onSearchChange={handleSearchChange}
                  onSearch={handleSearch}
                  onReset={handleReset}
                  loading={loading}
                />
              </CardContent>
            </Card>
          }
          primaryActions={[
            {
              action: 'add',
              config: {
                text: '新增用户',
                icon: Plus,
                onClick: handleAdd,
                disabled: loading,
              }
            }
          ]}
          secondaryActions={[
            {
              action: 'import',
              config: {
                text: '导入',
                icon: Upload,
                variant: 'outline',
                onClick: handleImport,
                disabled: loading,
              }
            },
            {
              action: 'export',
              config: {
                text: '导出',
                icon: Download,
                variant: 'outline',
                onClick: handleExport,
                disabled: loading,
              }
            },
            {
              action: 'export',
              config: {
                text: '同步主播和代理用户',
                icon: RotateCcw,
                variant: 'outline',
                onClick: handleSyncAnchorAndAgentUsers,
                disabled: loading,
              }
            }
          ]}
          customActions={
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              刷新
            </Button>
          }
        />



      {/* 用户表格 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <Filter className="w-5 h-5 mr-2" />
              用户列表
            </div>
            <div className="text-sm text-muted-foreground">
              共 {pagination.total} 条记录
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <UserTable
            data={users}
            loading={loading}
            selectedIds={selectedIds}
            onSelectionChange={setSelectedIds}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onResetPassword={handleResetPassword}
            onAssignRoles={handleAssignRoles}
            onToggleStatus={handleToggleStatus}
            pagination={{
              current: pagination.current,
              pageSize: pagination.pageSize,
              total: pagination.total,
              onChange: handlePaginationChange
            }}
            sortConfig={sortConfig}
            onSort={handleSort}
          />
        </CardContent>
      </Card>

      {/* 用户表单对话框 */}
      <UserForm
        open={formOpen}
        onOpenChange={setFormOpen}
        user={editingUser}
        onSuccess={handleFormSuccess}
      />

      {/* 重置密码对话框 */}
      <ResetPasswordDialog
        open={resetPasswordDialogOpen}
        onOpenChange={setResetPasswordDialogOpen}
        user={resetPasswordUser}
        onSuccess={handleResetPasswordSuccess}
      />

      {/* 角色分配对话框 */}
      <UserRoleAssignDialog
        open={roleAssignDialogOpen}
        onOpenChange={setRoleAssignDialogOpen}
        user={roleAssignUser}
        onSuccess={handleRoleAssignSuccess}
      />

      {/* 确认对话框 */}
      <ConfirmDialog
        open={confirmDialog.open}
        onOpenChange={(open) => setConfirmDialog(prev => ({ ...prev, open }))}
        title={confirmDialog.title}
        description={confirmDialog.description}
        variant="destructive"
        confirmText="确认"
        cancelText="取消"
        onConfirm={confirmDialog.onConfirm}
      />
      </div>
    </PagePermissionWrapper>
  )
}

export default UserList
